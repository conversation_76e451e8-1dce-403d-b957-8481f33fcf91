#!/usr/bin/env python3
"""
Test du client MCP avec la configuration existante
"""

import asyncio
import traceback
import json
import tempfile

from mcp_client import MCPClient


async def main():
    """Test avec la configuration existante dans claude_desktop_config.json"""
    # Créer un fichier de configuration temporaire avec votre configuration
    config = {
        "mcpServers": {
            "testMCPserver": {
                "command": "npx",
                "args": ["mcp-remote", "http://192.168.1.42:8000/mcp/", "--allow-http"],
            }
        }
    }
    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        json.dump(config, f, indent=2)
        config_path = f.name

    try:
        print("🔧 Initialisation du client MCP...")
        client = MCPClient()
        # client = MCPClient(config_path=config_path)

        servers = client.get_server_names()
        print(f"📋 Serveurs configurés: {servers}")
        if not servers:
            print("❌ Aucun serveur configuré trouvé.")
            return

        server_name = servers[0]
        print(f"🔌 Tentative de connexion au serveur '{server_name}'...")

        server_config = client.servers[server_name]
        print("📝 Configuration du serveur:")
        print(f"   - URL: {server_config.url}")
        print(f"   - Transport: {server_config.transport}")

        success = await client.connect_to_server(server_name)

        if success:
            print("✅ Connexion réussie!")

            print("🛠️  Récupération des outils...")
            tools = await client.list_server_tools(server_name)

            if tools:
                print(f"📦 Outils disponibles ({len(tools)}):")
                for tool in tools:
                    print(f"  - {tool['name']}: {tool['description']}")
            else:
                print("⚠️  Aucun outil trouvé")

            if tools:
                first_tool = tools[0]
                print(f"🧪 Test d'appel de l'outil '{first_tool['name']}'...")
                try:
                    # Adapter les arguments selon l'outil
                    if first_tool["name"] == "search_web":
                        args = {"query": "Python MCP", "max_results": 3}
                    else:
                        args = {}

                    result = await client.call_tool(
                        server_name, first_tool["name"], args
                    )
                    print(f"📤 Résultat: {result}")
                except Exception as e:
                    print(f"⚠️  Erreur lors de l'appel: {e}")
        else:
            print("❌ Échec de la connexion")
    except Exception as e:
        print(f"💥 Erreur: {e}")
        traceback.print_exc()
    finally:
        try:
            await client.close_all_connections()
            print("🔌 Connexions fermées")
        except:
            pass


if __name__ == "__main__":
    print("🚀 Test du client MCP avec configuration existante")
    print("=" * 50)
    asyncio.run(main())
    print("✨ Test terminé!")
