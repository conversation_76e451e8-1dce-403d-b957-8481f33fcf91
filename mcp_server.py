import socket
from typing import List, Dict, Any, Optional

# https://github.com/jlowin/fastmcp
from fastmcp import FastMCP
from fastmcp.utilities.logging import get_logger

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS
from ddgs.exceptions import (
    DDGSException,
    RatelimitException,
    TimeoutException,
)

mcp = FastMCP(
    name="DuckDuckGo Search",
    instructions="Effectue des recherches sur DuckDuckGo",
    version="1.0.0"
)
logger = get_logger("ddg_mcp")


@mcp.tool()
def search_web(
    query: str,
    max_results: int = 10,
    region: str = "fr-fr",
    safesearch: str = "off",
    backend: str = "auto",
    time_limit: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    Effectue une recherche web avec DuckDuckGo

    Args:
        query: Terme de recherche
        max_results: Nombre maximum de résultats (1-20, défaut: 10)
        region: Région de recherche (ex: 'us-en', 'fr-fr', 'wt-wt' pour worldwide)
        safesearch: Niveau de filtrage ('strict', 'moderate', 'off')
        time_limit: Limite temporelle ('d' pour jour, 'w' pour semaine, 'm' pour mois, 'y' pour année)

    Returns:
        Liste des résultats de recherche avec titre, URL, snippet et date
    """
    try:
        max_results = max(1, min(max_results, 20))

        if safesearch not in ["strict", "moderate", "off"]:
            safesearch = "moderate"

        if time_limit and time_limit not in ["d", "w", "m", "y"]:
            time_limit = None

        logger.info("Recherche web: '%s' (max_results=%d, region=%s)", query, max_results, region)

        with DDGS() as ddgs:
            results = list(
                ddgs.text(
                    query=query,
                    region=region,
                    safesearch=safesearch,
                    timelimit=time_limit,
                    backend=backend,
                    max_results=max_results,
                )
            )

        formatted_results = []
        for result in results:
            formatted_result = {
                "title": result.get("title", ""),
                "url": result.get("href", ""),
                "snippet": result.get("body", ""),
                "date": result.get("date", ""),
            }
            formatted_results.append(formatted_result)

        logger.info("Trouvé %d résultats pour '%s'", len(formatted_results), query)
        return formatted_results
    except RatelimitException as e:
        logger.error("Erreur Ratelimit: %s", str(e))
        return [{"error": f"Ratelimit: {str(e)}"}]
    except TimeoutException as e:
        logger.error("Erreur Timeout: %s", str(e))
        return [{"error": f"Timeout: {str(e)}"}]
    except DDGSException as e:
        logger.error("Erreur DDGS: %s", str(e))
        return [{"error": f"DDGS: {str(e)}"}]


def get_lan_ip() -> str:
    """Get the LAN IP of the machine (e.g., 192.168.x.x)."""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # Doesn’t need to be reachable, just used to get local IP
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except OSError:
        ip = "127.0.0.1"
    finally:
        s.close()
    return ip

def main():
    HOST = get_lan_ip()
    PORT = 8000

    mcp.run(transport="http", host=HOST, port=PORT)


if __name__ == "__main__":
    main()


'''
@mcp.tool()
def search_news(
    query: str,
    max_results: int = 10,
    region: str = "wt-wt",
    safesearch: str = "moderate",
    time_limit: str = "d"
) -> List[Dict[str, Any]]:
    """
    Effectue une recherche d'actualités avec DuckDuckGo

    Args:
        query: Terme de recherche
        max_results: Nombre maximum de résultats (1-20, défaut: 10)
        region: Région de recherche
        safesearch: Niveau de filtrage
        time_limit: Limite temporelle ('d', 'w', 'm')

    Returns:
        Liste des articles d'actualités
    """
    try:
        max_results = max(1, min(max_results, 20))

        if safesearch not in ['strict', 'moderate', 'off']:
            safesearch = 'moderate'

        if time_limit not in ['d', 'w', 'm']:
            time_limit = 'd'

        logger.info(f"Recherche actualités: '{query}' (max_results={max_results})")

        with DDGS() as ddgs:
            results = list(ddgs.news(
                keywords=query,
                region=region,
                safesearch=safesearch,
                timelimit=time_limit,
                max_results=max_results
            ))

        formatted_results = []
        for result in results:
            formatted_result = {
                'title': result.get('title', ''),
                'url': result.get('url', ''),
                'snippet': result.get('body', ''),
                'date': result.get('date', ''),
                'source': result.get('source', '')
            }
            formatted_results.append(formatted_result)

        logger.info(f"Trouvé {len(formatted_results)} articles pour '{query}'")
        return formatted_results

    except Exception as e:
        logger.error(f"Erreur lors de la recherche d'actualités: {str(e)}")
        return [{"error": f"Erreur lors de la recherche d'actualités: {str(e)}"}]

@mcp.tool()
def search_images(
    query: str,
    max_results: int = 10,
    region: str = "wt-wt",
    safesearch: str = "moderate",
    size: Optional[str] = None,
    type_image: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Effectue une recherche d'images avec DuckDuckGo

    Args:
        query: Terme de recherche
        max_results: Nombre maximum de résultats (1-100, défaut: 10)
        region: Région de recherche
        safesearch: Niveau de filtrage
        size: Taille des images ('Small', 'Medium', 'Large', 'Wallpaper')
        type_image: Type d'image ('photo', 'clipart', 'gif', 'transparent', 'line')

    Returns:
        Liste des images avec URL, titre et dimensions
    """
    try:
        max_results = max(1, min(max_results, 100))

        if safesearch not in ['strict', 'moderate', 'off']:
            safesearch = 'moderate'

        logger.info(f"Recherche images: '{query}' (max_results={max_results})")

        with DDGS() as ddgs:
            results = list(ddgs.images(
                keywords=query,
                region=region,
                safesearch=safesearch,
                size=size,
                type_image=type_image,
                max_results=max_results
            ))

        formatted_results = []
        for result in results:
            formatted_result = {
                'title': result.get('title', ''),
                'image_url': result.get('image', ''),
                'thumbnail_url': result.get('thumbnail', ''),
                'source_url': result.get('url', ''),
                'width': result.get('width', ''),
                'height': result.get('height', ''),
                'source': result.get('source', '')
            }
            formatted_results.append(formatted_result)

        logger.info(f"Trouvé {len(formatted_results)} images pour '{query}'")
        return formatted_results

    except Exception as e:
        logger.error(f"Erreur lors de la recherche d'images: {str(e)}")
        return [{"error": f"Erreur lors de la recherche d'images: {str(e)}"}]

@mcp.tool()
def instant_answer(query: str) -> Dict[str, Any]:
    """
    Obtient une réponse instantanée pour une requête

    Args:
        query: Question ou terme de recherche

    Returns:
        Réponse instantanée si disponible
    """
    try:
        logger.info(f"Recherche réponse instantanée: '{query}'")

        with DDGS() as ddgs:
            results = ddgs.answers(keywords=query)

        if results:
            return {
                'answer': results[0].get('text', ''),
                'topic': results[0].get('topic', ''),
                'url': results[0].get('url', '')
            }
        else:
            return {"message": "Aucune réponse instantanée trouvée"}

    except Exception as e:
        logger.error(f"Erreur lors de la recherche de réponse instantanée: {str(e)}")
        return {"error": f"Erreur lors de la recherche: {str(e)}"}

@mcp.tool()
def translate_text(
    text: str,
    from_lang: str = "auto",
    to_lang: str = "en"
) -> Dict[str, Any]:
    """
    Traduit un texte en utilisant le service de traduction de DuckDuckGo

    Args:
        text: Texte à traduire
        from_lang: Langue source (code à 2 lettres, 'auto' pour détection automatique)
        to_lang: Langue cible (code à 2 lettres)

    Returns:
        Texte traduit avec informations sur les langues
    """
    try:
        logger.info(f"Traduction: '{text[:50]}...' de {from_lang} vers {to_lang}")

        with DDGS() as ddgs:
            result = ddgs.translate(
                keywords=text,
                from_=from_lang,
                to=to_lang
            )

        if result:
            return {
                'original_text': text,
                'translated_text': result[0].get('translated', ''),
                'detected_language': result[0].get('detected_language', ''),
                'from_language': from_lang,
                'to_language': to_lang
            }
        else:
            return {"error": "Impossible de traduire le texte"}

    except Exception as e:
        logger.error(f"Erreur lors de la traduction: {str(e)}")
        return {"error": f"Erreur lors de la traduction: {str(e)}"}
'''
