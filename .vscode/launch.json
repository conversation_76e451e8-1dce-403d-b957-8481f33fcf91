{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
        {
            "name": "mcp-server",
            "type": "debugpy",
            "request": "launch",
            "program": "mcp-server.py",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
        {
            "name": "test_mcp_client",
            "type": "debugpy",
            "request": "launch",
            "program": "test_mcp_client.py",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
    ],
}
