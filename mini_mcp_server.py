import socket

# https://github.com/jlowin/fastmcp
from fastmcp import FastMCP



def get_lan_ip() -> str:
    """Get the LAN IP of the machine (e.g., 192.168.x.x)."""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # Doesn’t need to be reachable, just used to get local IP
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except OSError:
        ip = "127.0.0.1"
    finally:
        s.close()
    return ip


app_mcp = FastMCP(
    name="DuckDuckGo Search",
    instructions="Effectue des recherches sur DuckDuckGo",
    version="1.0.0",
)


@app_mcp.resource("hello/{name}")
def hello(name: str) -> str:
    return f"Hello, {name}!"


if __name__ == "__main__":
    app_mcp.run(transport="http", host="0.0.0.0", port=8000)
