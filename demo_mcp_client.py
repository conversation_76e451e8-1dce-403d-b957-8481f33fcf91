import asyncio
import json

from fastmcp import Client

from mcp_client import MC<PERSON><PERSON>


async def quick_test():
    client = Client("http://************:8000/mcp")
    async with client:
        tools = await client.list_tools()
        print(f"✓ Found {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool.name} : {tool.description}")



async def demo_basic_usage():
    """Démonstration basique du client MCP"""
    print("=== Démonstration du client MCP ===\n")

    client = MCPClient()

    servers = client.get_server_names()
    print(f"Serveurs configurés: {servers}")

    if not servers:
        print("Aucun serveur configuré. Créez un fichier claude_desktop_config.json")
        return

    print("Connexion aux serveurs...")
    await client.connect_to_all_servers()

    connected = client.get_connected_servers()
    print(f"Serveurs connectés: {connected}")

    if not connected:
        print("Aucune connexion établie.")
        return

    print("\n=== Outils disponibles ===")
    all_tools = await client.list_all_tools()

    for server_name, tools in all_tools.items():
        print(f"\n{server_name}:")
        if tools:
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
        else:
            print("  Aucun outil disponible")

    if "duckduckgo-search" in connected:
        print(f"\n=== Test du serveur duckduckgo-search ===")

        result = await client.call_tool(
            "duckduckgo-search",
            "search_web",
            {"query": "Python MCP protocol", "max_results": 3},
        )

        if result:
            print("Résultats de recherche web:")
            if isinstance(result, str):
                result = json.loads(result)

            if isinstance(result, list):
                for i, item in enumerate(result[:3], 1):
                    if isinstance(item, dict) and "title" in item:
                        print(f"  {i}. {item.get('title', 'Sans titre')}")
                        print(f"     URL: {item.get('url', 'N/A')}")
                        snippet = item.get("snippet", "")
                        if snippet:
                            print(f"     {snippet[:100]}...")
                        print()
            else:
                print(f"  {result}")

        result = await client.call_tool(
            "duckduckgo-search",
            "translate_text",
            {"text": "Hello, how are you?", "from_lang": "en", "to_lang": "fr"},
        )

        if result:
            print("Résultat de traduction:")
            print(f"  {result}")

    print("Fermeture des connexions...")
    await client.close_all_connections()


async def demo_specific_server():
    """Démonstration avec un serveur spécifique"""
    client = MCPClient()

    # Se connecter à un serveur spécifique
    server_name = "duckduckgo-search"

    print(f"Connexion au serveur {server_name}...")
    success = await client.connect_to_server(server_name)

    if not success:
        print(f"Impossible de se connecter au serveur {server_name}")
        return

    # Lister les outils de ce serveur
    tools = await client.list_server_tools(server_name)
    print(f"\nOutils disponibles sur {server_name}:")
    for tool in tools:
        print(f"  - {tool['name']}: {tool['description']}")

    # Test de plusieurs outils
    test_calls = [
        ("search_web", {"query": "MCP protocol", "max_results": 2}),
        ("instant_answer", {"query": "What is Python?"}),
        (
            "translate_text",
            {"text": "Bonjour le monde", "from_lang": "fr", "to_lang": "en"},
        ),
    ]

    for tool_name, args in test_calls:
        print(f"\n=== Test de {tool_name} ===")
        result = await client.call_tool(server_name, tool_name, args)
        if result:
            print(f"Résultat: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("Aucun résultat")

    await client.close_all_connections()


def main():
    #asyncio.run(quick_test())
    asyncio.run(demo_basic_usage())
    # asyncio.run(demo_specific_server())


if __name__ == "__main__":
    main()
