import json
import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from fastmcp.utilities.logging import get_logger

logger = get_logger(__name__)


@dataclass
class MCPServerConfig:
    """Configuration d'un serveur MCP"""

    name: str
    command: str
    args: List[str]
    env: Dict[str, str]
    cwd: Optional[str] = None


class MCPClient:
    """Client MCP"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._find_claude_config()
        self.servers: Dict[str, MCPServerConfig] = {}
        self.sessions: Dict[str, ClientSession] = {}
        self.load_config()

    def _find_claude_config(self) -> str:
        """Trouve le fichier de configuration Claude Desktop"""
        possible_paths = []

        if sys.platform == "darwin":  # macOS
            home = Path.home()
            possible_paths = [
                home
                / "Library"
                / "Application Support"
                / "Claude"
                / "claude_desktop_config.json",
                home / ".config" / "claude" / "claude_desktop_config.json",
            ]
        elif sys.platform == "win32":  # Windows
            appdata = os.getenv("APPDATA")
            if appdata:
                possible_paths = [
                    Path(appdata) / "Claude" / "claude_desktop_config.json"
                ]
        else:  # Linux et autres
            home = Path.home()
            possible_paths = [
                home / ".config" / "claude" / "claude_desktop_config.json",
                home / ".claude" / "claude_desktop_config.json",
            ]

        possible_paths.append(Path.cwd() / "claude_desktop_config.json")

        for path in possible_paths:
            if path.exists():
                logger.info("Configuration trouvée: %s", path)
                return str(path)

        logger.warning("Aucun fichier 'claude_desktop_config.json' trouvé")
        return "claude_desktop_config.json"

    def load_config(self):
        """Charge la configuration depuis le fichier JSON"""
        try:
            if not os.path.exists(self.config_path):
                logger.error(
                    "Fichier de configuration non trouvé: %s", self.config_path
                )
                return

            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            mcp_servers = config.get("mcpServers", {})

            for name, server_config in mcp_servers.items():
                self.servers[name] = MCPServerConfig(
                    name=name,
                    command=server_config.get("command", ""),
                    args=server_config.get("args", []),
                    env=server_config.get("env", {}),
                    cwd=server_config.get("cwd"),
                )

            logger.info(
                f"Chargé {len(self.servers)} serveurs MCP: {list(self.servers.keys())}"
            )
        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration: {e}")

    async def connect_to_server(self, server_name: str) -> bool:
        """Se connecte à un serveur MCP spécifique"""
        if server_name not in self.servers:
            logger.error("Serveur '%s' non trouvé dans la configuration", server_name)
            return False

        server_config = self.servers[server_name]

        try:
            server_params = StdioServerParameters(
                command=server_config.command,
                args=server_config.args,
                env={**os.environ, **server_config.env},
            )

            async with stdio_client(server_params) as stdio_transport:
                session = ClientSession(stdio_transport[0], stdio_transport[1])
                await session.initialize()
                self.sessions[server_name] = session
                logger.info("Connecté au serveur '%s'", server_name)
                return True
        except Exception as e:
            logger.error("Erreur lors de la connexion à '%s' : %s", server_name, e)
            return False

    async def disconnect_from_server(self, server_name: str):
        """Se déconnecte d'un serveur MCP"""
        if server_name in self.sessions:
            try:
                del self.sessions[server_name]
                logger.info("Déconnecté du serveur '%s'", server_name)
            except KeyError:
                logger.error("Pas de session active pour '%s'", server_name)
            except Exception as e:
                logger.error(
                    "Erreur : %s, lors de la déconnexion de '%s'", e, server_name
                )

    async def connect_to_all_servers(self):
        """Se connecte à tous les serveurs configurés"""
        tasks = []
        for server_name in self.servers.keys():
            tasks.append(self.connect_to_server(server_name))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        connected = sum(1 for result in results if result is True)
        logger.info("Connecté à %d serveurs", connected / len(self.servers))

    async def list_server_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """Liste les outils disponibles sur un serveur"""
        if server_name not in self.sessions:
            logger.error(f"Pas de session active pour '{server_name}'")
            return []

        try:
            session = self.sessions[server_name]
            tools = await session.list_tools()
            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.inputSchema,
                }
                for tool in tools.tools
            ]
        except Exception as e:
            logger.error(
                f"Erreur lors de la récupération des outils de '{server_name}': {e}"
            )
            return []

    async def call_tool(
        self, server_name: str, tool_name: str, arguments: Dict[str, Any]
    ) -> Any:
        """Appelle un outil sur un serveur spécifique"""
        if server_name not in self.sessions:
            logger.error(f"Pas de session active pour '{server_name}'")
            return None

        try:
            session = self.sessions[server_name]
            result = await session.call_tool(tool_name, arguments)

            if result.isError:
                logger.error(f"Erreur lors de l'appel de l'outil: {result.content}")
                return None

            content = []
            for item in result.content:
                if hasattr(item, "text"):
                    content.append(item.text)
                elif hasattr(item, "data"):
                    content.append(item.data)
                else:
                    content.append(str(item))

            return content[0] if len(content) == 1 else content
        except Exception as e:
            logger.error(
                f"Erreur lors de l'appel de l'outil '{tool_name}' sur '{server_name}': {e}"
            )
            return None

    async def list_all_tools(self) -> Dict[str, List[Dict[str, Any]]]:
        """Liste tous les outils de tous les serveurs connectés"""
        all_tools = {}

        for server_name in self.sessions.keys():
            tools = await self.list_server_tools(server_name)
            all_tools[server_name] = tools

        return all_tools

    def get_server_names(self) -> List[str]:
        """Retourne la liste des noms de serveurs configurés"""
        return list(self.servers.keys())

    def get_connected_servers(self) -> List[str]:
        """Retourne la liste des serveurs actuellement connectés"""
        return list(self.sessions.keys())

    async def close_all_connections(self):
        """Ferme toutes les connexions aux serveurs"""
        for server_name in list(self.sessions.keys()):
            await self.disconnect_from_server(server_name)
