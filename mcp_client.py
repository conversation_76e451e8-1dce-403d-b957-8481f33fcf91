import json
import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport, SSETransport
from fastmcp.utilities.logging import get_logger

logger = get_logger(__name__)


@dataclass
class MCPServerConfig:
    """Configuration d'un serveur MCP"""

    name: str
    # Pour les serveurs stdio
    command: Optional[str] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    cwd: Optional[str] = None
    # Pour les serveurs réseau
    url: Optional[str] = None
    transport: Optional[str] = None  # "http", "sse", etc.
    headers: Optional[Dict[str, str]] = None
    auth: Optional[str] = None


class MCPClient:
    """Client MCP"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._find_mcp_config()
        self.servers: Dict[str, MCPServerConfig] = {}
        self.clients: Dict[str, Client] = {}
        self.load_config()

    def _find_mcp_config(self) -> str:
        """Trouve le fichier de configuration MCP"""
        possible_paths = []

        if sys.platform == "darwin":  # macOS
            home = Path.home()
            possible_paths = [
                home
                / "Library"
                / "Application Support"
                / "mcp.json",
                home / ".config" / "mcp.json",
            ]
        elif sys.platform == "win32":  # Windows
            appdata = os.getenv("APPDATA")
            if appdata:
                possible_paths = [
                    Path(appdata) / "mcp.json"
                ]
        else:  # Linux et autres
            home = Path.home()
            possible_paths = [
                home / ".config" / "mcp.json",
            ]

        possible_paths.append(Path.cwd() / "mcp.json")

        for path in possible_paths:
            if path.exists():
                logger.info("Configuration trouvée: %s", path)
                return str(path)

        logger.warning("Aucun fichier 'mcp.json' trouvé")
        return ""

    def load_config(self):
        """Charge la configuration depuis le fichier JSON"""
        try:
            if not os.path.exists(self.config_path):
                logger.error(
                    "Fichier de configuration non trouvé: %s", self.config_path
                )
                return

            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            mcp_servers = config.get("mcpServers", {})

            for name, server_config in mcp_servers.items():
                # Détection du type de serveur basé sur la configuration
                if "url" in server_config:
                    # Configuration réseau directe
                    self.servers[name] = MCPServerConfig(
                        name=name,
                        url=server_config.get("url"),
                        transport=server_config.get("transport"),
                        headers=server_config.get("headers", {}),
                        auth=server_config.get("auth"),
                    )
                elif "command" in server_config:
                    # Vérifier si c'est une configuration mcp-remote
                    command = server_config.get("command")
                    args = server_config.get("args", [])

                    # Détecter mcp-remote
                    if command in ["npx", "node"] and "mcp-remote" in args:
                        # Extraire l'URL des arguments
                        url = None
                        logger.debug(f"Parsing mcp-remote args for {name}: {args}")
                        for arg in args:
                            if arg.startswith("http"):
                                url = arg
                                break

                        if url:
                            logger.info(f"Serveur mcp-remote détecté: {name} -> {url}")
                            self.servers[name] = MCPServerConfig(
                                name=name,
                                url=url,
                                transport="http",  # Par défaut pour mcp-remote
                            )
                        else:
                            logger.warning(f"Configuration mcp-remote invalide pour '{name}' - URL non trouvée dans les args: {args}")
                            continue
                    else:
                        # Configuration stdio classique
                        self.servers[name] = MCPServerConfig(
                            name=name,
                            command=server_config.get("command"),
                            args=server_config.get("args", []),
                            env=server_config.get("env", {}),
                            cwd=server_config.get("cwd"),
                        )
                else:
                    logger.warning(f"Configuration invalide pour le serveur '{name}' - ni URL ni command trouvées")
                    continue

            logger.info(
                f"Chargé {len(self.servers)} serveurs MCP: {list(self.servers.keys())}"
            )
        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration: {e}")

    async def connect_to_server(self, server_name: str) -> bool:
        """Se connecte à un serveur MCP spécifique"""
        if server_name not in self.servers:
            logger.error("Serveur '%s' non trouvé dans la configuration", server_name)
            return False

        server_config = self.servers[server_name]

        try:
            # Créer le transport approprié selon la configuration
            if server_config.url:
                # Configuration réseau
                if server_config.transport == "sse" or (server_config.url and server_config.url.endswith("/sse")):
                    transport = SSETransport(
                        url=server_config.url,
                        headers=server_config.headers or {},
                        auth=server_config.auth
                    )
                else:
                    # Par défaut, utiliser StreamableHttpTransport
                    transport = StreamableHttpTransport(
                        url=server_config.url,
                        headers=server_config.headers or {},
                        auth=server_config.auth
                    )

                # Créer le client avec le transport réseau
                client = Client(transport)
                self.clients[server_name] = client

                logger.info("Connecté au serveur réseau '%s' à l'URL: %s", server_name, server_config.url)
                return True
            else:
                logger.error("Configuration de serveur non supportée pour '%s'", server_name)
                return False

        except Exception as e:
            logger.error("Erreur lors de la connexion à '%s' : %s", server_name, e)
            return False

    async def disconnect_from_server(self, server_name: str):
        """Se déconnecte d'un serveur MCP"""
        if server_name in self.clients:
            try:
                client = self.clients[server_name]
                # Fermer le client s'il est dans un contexte async
                if hasattr(client, '__aexit__'):
                    await client.__aexit__(None, None, None)
                del self.clients[server_name]
                logger.info("Déconnecté du serveur '%s'", server_name)
            except KeyError:
                logger.error("Pas de client actif pour '%s'", server_name)
            except Exception as e:
                logger.error(
                    "Erreur : %s, lors de la déconnexion de '%s'", e, server_name
                )

    async def connect_to_all_servers(self):
        """Se connecte à tous les serveurs configurés"""
        tasks = []
        for server_name in self.servers.keys():
            tasks.append(self.connect_to_server(server_name))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        connected = sum(1 for result in results if result is True)
        logger.info("Connecté à %d serveurs", connected / len(self.servers))

    async def list_server_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """Liste les outils disponibles sur un serveur"""
        if server_name not in self.clients:
            logger.error(f"Pas de client actif pour '{server_name}'")
            return []

        try:
            client = self.clients[server_name]
            async with client:
                tools = await client.list_tools()
                return [
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.inputSchema,
                    }
                    for tool in tools
                ]
        except Exception as e:
            logger.error(
                f"Erreur lors de la récupération des outils de '{server_name}': {e}"
            )
            return []

    async def call_tool(
        self, server_name: str, tool_name: str, arguments: Dict[str, Any]
    ) -> Any:
        """Appelle un outil sur un serveur spécifique"""
        if server_name not in self.clients:
            logger.error(f"Pas de client actif pour '{server_name}'")
            return None

        try:
            client = self.clients[server_name]
            async with client:
                result = await client.call_tool(tool_name, arguments)
                return result
        except Exception as e:
            logger.error(
                f"Erreur lors de l'appel de l'outil '{tool_name}' sur '{server_name}': {e}"
            )
            return None

    async def list_all_tools(self) -> Dict[str, List[Dict[str, Any]]]:
        """Liste tous les outils de tous les serveurs connectés"""
        all_tools = {}

        for server_name in self.clients.keys():
            tools = await self.list_server_tools(server_name)
            all_tools[server_name] = tools

        return all_tools

    def get_server_names(self) -> List[str]:
        """Retourne la liste des noms de serveurs configurés"""
        return list(self.servers.keys())

    def get_connected_servers(self) -> List[str]:
        """Retourne la liste des serveurs actuellement connectés"""
        return list(self.clients.keys())

    async def close_all_connections(self):
        """Ferme toutes les connexions aux serveurs"""
        for server_name in list(self.clients.keys()):
            await self.disconnect_from_server(server_name)
