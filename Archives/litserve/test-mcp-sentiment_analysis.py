import os
import requests

SERVER_HOST = os.environ.get("SERVER_HOST", "127.0.0.1")
"""
Prompt <PERSON> :
Utilisez l'outil MCP pour déterminer le sentiment de ce texte : "Ce produit fonctionne comme prévu."
"""
PAYLOAD = {"input": "Ce produit fonctionne comme prévu."}

try:
    response = requests.post(
        url=f"http://{SERVER_HOST}:8000/predict", json=PAYLOAD, timeout=10
    )
    response.raise_for_status()
    print(response.json())
except requests.exceptions.RequestException as e:
    print(f"Error connecting to the server: {e}")
    print(
        f"Please ensure the server is running and accessible at http://{SERVER_HOST}:8000"
    )
