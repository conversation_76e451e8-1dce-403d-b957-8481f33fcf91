from transformers import pipeline

# https://huggingface.co/cmarkea/distilcamembert-base-sentiment
# Les labels renvoyés par ce modèle sont :
#   1 star (très négatif)
#   2 stars (négatif)
#   3 stars (neutre / mitigé)
#   4 stars (positif)
#   5 stars (très positif)
sentiment_analyzer = pipeline(
    "sentiment-analysis",
    model="cmarkea/distilcamembert-base-sentiment",
    tokenizer="cmarkea/distilcamembert-base-sentiment",
)

label_map = {
    "1 star": "très négatif",
    "2 stars": "négatif",
    "3 stars": "mitigé",
    "4 stars": "positif",
    "5 stars": "très positif"
}

phrases = [
    "Ce restaurant est exceptionnel, le service était impeccable !",
    "Je suis extrêmement déçu, je ne reviendrai jamais.",
    "C'était correct, mais sans plus, un peu décevant.",
    "Un produit vraiment génial, je le recommande fortement.",
    "Le film était trop long et ennuyeux."
]

for phrase in phrases:
    result = sentiment_analyzer(phrase)[0]
    sentiment = label_map.get(result['label'], result['label'])
    print(f"Texte : {phrase}")
    print(f"Sentiment : {sentiment} | Score : {result['score']:.4f}")
    print("-" * 60)
