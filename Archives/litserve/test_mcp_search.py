#!/usr/bin/env python3
"""Test script to verify the MCP DuckDuckGo search tool works correctly."""

import asyncio
import json
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def test_mcp_search():
    """Test the MCP search functionality."""
    
    # For testing, we'll make a direct HTTP request to the MCP endpoint
    import httpx
    
    # Test the MCP tools list endpoint
    async with httpx.AsyncClient() as client:
        try:
            # First, let's check if the server is responding
            response = await client.get("http://localhost:8002/")
            print(f"Server status: {response.status_code} - {response.text}")
            
            # Test the MCP endpoint
            mcp_response = await client.get("http://localhost:8002/mcp/")
            print(f"MCP endpoint status: {mcp_response.status_code}")
            
            # Test calling the search tool via HTTP POST to the websearch endpoint
            search_data = {"query": "Python programming"}
            search_response = await client.post(
                "http://localhost:8002/websearch",
                json=search_data
            )
            print(f"Search response status: {search_response.status_code}")
            if search_response.status_code == 200:
                result = search_response.json()
                print(f"Search results: {json.dumps(result, indent=2)}")
            else:
                print(f"Search error: {search_response.text}")
                
        except Exception as e:
            print(f"Error testing MCP: {e}")


if __name__ == "__main__":
    asyncio.run(test_mcp_search())
