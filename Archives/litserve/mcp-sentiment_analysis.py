from pydantic import BaseModel

# https://github.com/Lightning-AI/LitServe
# https://lightning.ai/docs/litserve/features/mcp
import litserve as ls
from litserve.mcp import MCP

# https://github.com/huggingface/transformers
from transformers import pipeline


class InputRequest(BaseModel):
    input: str


class TextClassificationAPI(ls.LitAPI):
    _label_map = {
        "1 star": "très négatif",
        "2 stars": "négatif",
        "3 stars": "mitigé",
        "4 stars": "positif",
        "5 stars": "très positif"
    }

    def setup(self, device):
        # https://huggingface.co/cmarkea/distilcamembert-base-sentiment
        # Les labels renvoyés par ce modèle sont :
        #   1 star (très négatif)
        #   2 stars (négatif)
        #   3 stars (neutre / mitigé)
        #   4 stars (positif)
        #   5 stars (très positif)
        self._model = pipeline(
            task="sentiment-analysis",
            model="cmarkea/distilcamembert-base-sentiment",
            tokenizer="cmarkea/distilcamembert-base-sentiment",
            device=device,
        )

    def decode_request(self, request: InputRequest, **kwargs):
        return request.input

    def predict(self, x, **kwargs):
        return self._model(x)

    def encode_response(self, output, **kwargs):
        return {
            "sentiment": self._label_map.get(output[0]['label'], output[0]['label']),
            "score": output[0]['score']
        }


if __name__ == "__main__":
    mcp = MCP(
        name="sentiment_classifier",
        description="Analyse de sentiment d'un texte en français. Retourne un score de sentiment et un label correspondant.",
    )


    api = TextClassificationAPI(mcp=mcp)
    server = ls.LitServer(api)
    server.run(host="0.0.0.0", port=8000)
    # FastAPI Swagger UI : http://************:8000/docs