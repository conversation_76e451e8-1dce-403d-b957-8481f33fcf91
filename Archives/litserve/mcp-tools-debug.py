from pydantic import BaseModel
from typing import List
import logging
import sys

import litserve as ls
from litserve.mcp import MCP
from litserve.utils import configure_logging

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS

# Enhanced debug logging configuration
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug.log')
    ]
)

# Configure LitServe logging with enhanced debug info
configure_logging(level=logging.INFO, use_rich=True)
logger = logging.getLogger("litserve")

# Enable debug logging for specific modules
debug_loggers = [
    "litserve",
    "litserve.mcp", 
    "litserve.server",
    "uvicorn",
    "fastapi"
]

for logger_name in debug_loggers:
    debug_logger = logging.getLogger(logger_name)
    debug_logger.setLevel(logging.INFO)  # Use INFO instead of DEBUG to avoid LitServe bug


class SearchRequest(BaseModel):
    query: str


class WebSearchResult(BaseModel):
    url: str
    title: str
    description: str | None = None


class WebSearchResponse(BaseModel):
    results: List[WebSearchResult]


class DuckDuckGoSearchAPI(ls.LitAPI):
    def setup(self, device):
        logger.info(f"Setting up DuckDuckGoSearchAPI on device: {device}")
        pass

    def decode_request(self, request: SearchRequest, **kwargs):
        logger.info(f"🔍 DECODE_REQUEST - Received: {request}")
        logger.info(f"🔍 DECODE_REQUEST - Type: {type(request)}")
        logger.info(f"🔍 DECODE_REQUEST - Query: {request.query}")
        logger.info(f"🔍 DECODE_REQUEST - Kwargs: {kwargs}")
        
        return request.query

    def predict(self, x: str, **kwargs):
        logger.info(f"🚀 PREDICT - Starting search for: '{x}'")
        logger.info(f"🚀 PREDICT - Kwargs: {kwargs}")
        
        try:
            with DDGS() as ddgs:
                logger.info("🌐 Executing DuckDuckGo search...")
                results = list(ddgs.text(x, max_results=5))
                logger.info(f"🌐 Found {len(results)} raw results")
                
                search_results = []
                for i, result in enumerate(results):
                    logger.debug(f"🌐 Processing result {i+1}: {result.get('title', 'No title')}")
                    search_results.append(
                        WebSearchResult(
                            url=result.get("href", ""),
                            title=result.get("title", ""),
                            description=result.get("body", ""),
                        )
                    )
                
                response = WebSearchResponse(results=search_results)
                logger.info(f"✅ PREDICT - Returning {len(search_results)} formatted results")
                return response
                
        except Exception as e:
            logger.error(f"❌ PREDICT - Error during search: {e}")
            logger.exception("Full traceback:")
            raise


if __name__ == "__main__":
    logger.info("🚀 Starting MCP Server in DEBUG mode...")

    duckduckgo_mcp = MCP(
        name="duckduckgo_search",
        description="Effectue une recherche sur DuckDuckGo et retourne les résultats.",
        input_schema={
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "La requête de recherche"}
            },
            "required": ["query"],
        },
    )

    logger.info("🔧 Configuring MCP server...")
    server = ls.LitServer(
        [
            DuckDuckGoSearchAPI(mcp=duckduckgo_mcp, api_path="/websearch"),
        ],
    )

    # Patch LitServe's MCP implementation to add missing handlers
    from litserve.mcp import _LitMCPServerConnector

    original_mount_with_fastapi = _LitMCPServerConnector._mount_with_fastapi

    def patched_mount_with_fastapi(self, app):
        logger.info("🔧 Applying MCP patches...")
        original_mount_with_fastapi(self, app)

        @self.mcp_server.list_prompts()
        async def _list_prompts():
            logger.debug("📝 list_prompts called, returning empty list")
            return []

        @self.mcp_server.list_resources()
        async def _list_resources():
            logger.debug("📚 list_resources called, returning empty list")
            return []

        logger.info("✅ Added missing MCP handlers for prompts and resources")

    _LitMCPServerConnector._mount_with_fastapi = patched_mount_with_fastapi

    logger.info("🌐 Starting server on port 8003...")
    server.run(
        port=8003,  # Use different port to avoid conflicts
        log_level="info",    # Keep at info level
        pretty_logs=True,    # Enable rich formatting
        num_api_servers=1    # Single server for easier debugging
    )
