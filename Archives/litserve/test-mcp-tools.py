from typing import Any, Dict, Optional

import os
import requests

SERVER_HOST = os.environ.get("SERVER_HOST", "127.0.0.1")
BASE_URL = f"http://{SERVER_HOST}:8000"
TIMEOUT = 10


def call_api(endpoint: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Generic function to call a server API endpoint."""
    try:
        response = requests.post(
            url=f"{BASE_URL}/{endpoint}", json=payload, timeout=TIMEOUT
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to the server: {e}")
        print(f"Please ensure the server is running and accessible at {BASE_URL}")
        return None


def call_add(a: int, b: int) -> Optional[Dict[str, Any]]:
    """Calls the /add endpoint."""
    return call_api("add", {"a": a, "b": b})


def call_reverse(text: str) -> Optional[Dict[str, Any]]:
    """Calls the /reverse endpoint."""
    return call_api("reverse", {"text": text})


def call_upper(text: str) -> Optional[Dict[str, Any]]:
    """Calls the /upper endpoint."""
    return call_api("upper", {"text": text})


if __name__ == "__main__":
    if add_result := call_add(7, 4):
        print("Add:", add_result)  # → {"result": 11}
    if reverse_result := call_reverse("chatgpt"):
        print("Reverse:", reverse_result)  # → {"reversed": "tpgtahc"}
    if upper_result := call_upper("salut"):
        print("Upper:", upper_result)  # → {"upper": "SALUT"}
