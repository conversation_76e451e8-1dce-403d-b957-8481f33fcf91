from pydantic import BaseModel
import litserve as ls
from litserve.mcp import MCP


class SimpleRequest(BaseModel):
    message: str


class SimpleAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: SimpleRequest, **kwargs):
        return request.message

    def predict(self, x: str, **kwargs):
        return f"Echo: {x}"


if __name__ == "__main__":
    mcp = MCP(
        name="simple_echo",
        description="Simple echo tool for testing MCP integration.",
        input_schema={
            "type": "object",
            "properties": {
                "message": {"type": "string", "description": "Message to echo"}
            },
            "required": ["message"],
        },
    )

    api = SimpleAPI(mcp=mcp, api_path="/echo")
    server = ls.LitServer(api)
    server.run(port=8003)
