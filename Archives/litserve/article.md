---
meta:
  description: "Déployez un serveur MCP avec LitServe et connectez vos modèles IA à Claude Desktop facilement. Intégration rapide et standardisée via MCP."
  keywords: [serveur, LitServe, MCP, <PERSON>, Model Context Protocol, déploiement modèle, IA, FastAPI, intégration, LitServe, Hugging Face.]
---

# Mise en œuvre d’un serveur MCP avec LitServe et connexion à Claude Desktop

## Pourquoi utiliser le protocole MCP pour vos modèles IA ?

Connecter des modèles d’IA à des applications nécessite souvent un **codage personnalisé** :

- Chaque intégration (Slack, dashboard, bot, IDE) demande un code différent.
- Les workflows deviennent fragmentés avec des **API REST spécifiques** ou des wrappers maison.

👉 Le protocole [MCP](https://modelcontextprotocol.io/docs/getting-started/intro) (Model Context Protocol) standardise tout cela et simplifie l’intégration.

## LitServe et MCP : simplifier l’intégration des modèles d’IA

[**LitServe**](https://github.com/Lightning-AI/LitServe) est un moteur open source basé sur **FastAPI**.
Il propose désormais un point de terminaison **/mcp/** permettant de rendre n’importe quel modèle IA compatible MCP.

- Aucun changement dans la logique du modèle.
- Compatible avec **Claude Desktop**, **Cursor IDE**, ou tout client MCP.
- Supporte **Hugging Face**, **RAG**, **agents IA** et plus encore.

## Exemple complet : serveur MCP avec Hugging Face et LitServe

Voici un exemple de **serveur MCP d’analyse de sentiments** avec LitServe :

```python
from pydantic import BaseModel
import litserve as ls
from litserve.mcp import MCP
from transformers import pipeline

class InputRequest(BaseModel):
    input: str

class TextClassificationAPI(ls.LitAPI):
    _label_map = {
        "1 star": "très négatif",
        "2 stars": "négatif",
        "3 stars": "mitigé",
        "4 stars": "positif",
        "5 stars": "très positif"
    }

    def setup(self, device):
        self._model = pipeline(
            task="sentiment-analysis",
            model="cmarkea/distilcamembert-base-sentiment",
            tokenizer="cmarkea/distilcamembert-base-sentiment",
            device=device,
        )

    def decode_request(self, request: InputRequest, **kwargs):
        return request.input

    def predict(self, x, **kwargs):
        return self._model(x)

    def encode_response(self, output, **kwargs):
        return {
            "sentiment": self._label_map.get(output[0]['label'], output[0]['label']),
            "score": output[0]['score']
        }

if __name__ == "__main__":
    mcp = MCP(
        name="sentiment_classifier",
        description="Analyse de sentiment en français avec score et label.",
    )
    api = TextClassificationAPI(mcp=mcp)
    server = ls.LitServer(api)
    server.run(host="0.0.0.0", port=8000)
```

👉 Une fois lancé (`python server.py`), votre modèle est accessible :

- en **REST API classique**

- via **MCP : [http://localhost:8000/mcp](http://localhost:8000/mcp)**

## Comment connecter LitServe MCP à Claude Desktop ?

### Étape 1 : Lancer le serveur MCP

```bash
python server.py
```

### Étape 2 : Installer mcp-remote (si nécessaire)

```bash
npm install -g mcp-remote
```

### Étape 3 : Modifier la configuration MCP de Claude Desktop

- **macOS** : `~/Library/Application Support/Claude/claude_desktop_config.json`

- **Linux** : `~/.config/claude/claude_desktop_config.json`

- **Windows** : `%APPDATA%\Claude\claude_desktop_config.json`

Ajoutez :

```json
{
  "mcpServers": {
    "litserve": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8000/mcp/",
        "--allow-http"
      ]
    }
  }
}
```

### Étape 4 : Redémarrer Claude Desktop

Une icône d'outil (🛠️ ou un curseur) devrait maintenant apparaître à côté du champ de saisie.

![Nous pouvons voir Litserve dans le menu après une connexion réussie](Litserve.png)

👉 l'outil d'analyse de sentiment est désormais disponible dans Claude Desktop.

Lorsque MCP est activé :

- `LitServe` enregistre un outil à l'aide du [schéma d'outil MCP](https://modelcontextprotocol.io/specification/2025-06-18/server/tools).
- L'outil est monté sur `/mcp/` à l'aide d'un gestionnaire HTTP streamable.
- Les clients (par exemple, un LLM ou un environnement de développement) peuvent utiliser
  - `tools/list` : Pour découvrir la liste des outils disponibles qu'un serveur MCP expose.
  - `tools/call` : Une fois qu'un client (ou un LLM) a identifié un outil pertinent via `tools/list` et a décidé de l'utiliser, il invoque cet outil en envoyant une requête structurée à cettte URL.

![Utilisation de l'outil MCP dans Claude Desktop](Tool.png)

## Les avantages d’un serveur MCP pour vos intégrations IA

🔹 **Standardisation** : plus besoin d’API multiples.

🔹 **Compatibilité** : fonctionne avec Claude Desktop, Cursor, etc.

🔹 **Simplicité** : aucune modification du code modèle.

🔹 **Scalabilité** : supporte agents, RAG, modèles Hugging Face.

Nous apprécions `LitServe` pour les raisons suivantes :

- Il est plus rapide que FastAPI.
- Possibilité
  - de servir n'importe quel modèle d'IA (LLM, vision, audio, multimodal).
  - de composer des agents, des RAG et des pipelines dans un seul fichier.
  - d'ajouter une logique personnalisée et avoir un contrôle total sur l'inférence.

## Conclusion : vers une standardisation des intégrations IA

Grâce à la nouvelle intégration du **protocole MCP** de **LitServe**, nous pouvons connecter des APIs directement aux outils d'IA et aux IDE de bureau les plus répandus, sans aucun code de liaison. Ainsi l’intégration des modèles IA devient fluide, rapide et standardisée.

Cela ouvre la voie à de nouveaux modes d'interaction riches : classification, chat, résumé, etc. Cette approche permettant d’accélérer le déploiement et d’unifier les workflows IA.

👉 Démarrez votre **serveur MCP** et laissez vos modèles rejoindre l'écosystème de l'IA !
